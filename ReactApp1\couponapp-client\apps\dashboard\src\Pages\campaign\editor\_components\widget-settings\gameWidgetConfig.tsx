import { GameWidget, GameWidgetSettings } from '@repo/shared/components/react-templates/blocks/game'
import { MarginControl, PaddingControl, SizeControl } from './shared/layout-controls'
import { addPropertyControls } from '../../property-controls'
import { useEditor } from '@/lib/hooks/useEditor'
import { Label } from '@repo/shared/components/ui/label'
import { Widget } from '@repo/shared/lib/types/editor'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Button } from '@repo/shared/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@repo/shared/components/ui/popover'
import { BorderRadiusControl } from './shared/border-controls'
import { getGameModule, loadGameModule, gameModulesList } from '@repo/shared/lib/game/gameRegistry'
import { useState, useEffect, useContext, useMemo } from 'react'
import { Suspense } from 'react'
import { useWidgetSettings } from '@/lib/hooks/useWidgetSettings'
import { skinsDirectory } from '@repo/shared/lib/game/gameSkinsTest'
import { applySkin, GameSkin } from '@repo/shared/lib/game/gameSkin'

function GameTypeControl(props: { widget: Widget }) {
    const games = gameModulesList
    const { settings, updateSettings } = useWidgetSettings<GameWidgetSettings>(props.widget)
    const [currentGame, setCurrentGame] = useState(settings?.gameId ?? '2048')

    const handleGameChange = async (value: string) => {
        setCurrentGame(value)

        let gameModule = getGameModule(value)
        if (gameModule == null) {
            await loadGameModule(value)
            gameModule = getGameModule(value)
        }

        //TODO batch update of these!
        updateSettings({
            gameId: value,
            gameConfig: gameModule.defaultConfig,
        })
    }

    return (
        <div className="space-y-2">
            <Label>Game</Label>
            <Select value={currentGame} onValueChange={handleGameChange}>
                <SelectTrigger>
                    <SelectValue />
                </SelectTrigger>
                <SelectContent>
                    {games.map((game) => (
                        <SelectItem key={game} value={game}>
                            {game}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    )
}

function GameSettingsControl(props: { widget: Widget }) {
    const { settings, updateSettings } = useWidgetSettings<GameWidgetSettings>(props.widget)
    const [error, setError] = useState<Error | null>(null)

    const [EditorComponent, setEditorComponent] = useState<any>(null)
    const [SingleConfigEditComponent, setSingleConfigEditComponent] = useState<any>(null)

    useEffect(() => {
        const loadEditor = async () => {
            try {
                const gameModule = await loadGameModule(settings?.gameId)
                if (gameModule && gameModule.editorComponent) {
                    setEditorComponent(() => gameModule.editorComponent)
                }
                if (gameModule && gameModule.configKeyEditor) {
                    setSingleConfigEditComponent(() => gameModule.configKeyEditor)
                }
            } catch (err) {
                setError(err instanceof Error ? err : new Error('Failed to load game editor'))
                console.error('Failed to load game editor:', err)
            }
        }

        loadEditor()
    }, [settings?.gameId])

    const updateConfig = (config: any) => {
        updateSettings({
            gameConfig: config,
        })
    }

    if (error) {
        return (
            <div className="space-y-2">
                <div className="text-red-500">Error loading game editor: {error.message}</div>
            </div>
        )
    }

    return (
        <div className="space-y-4">
            <Suspense fallback={<div>Loading game settings editor...</div>}>{EditorComponent && <EditorComponent config={settings?.gameConfig} updateConfig={updateConfig} />}</Suspense>
        </div>
    )
}

function SkinSelectorControl(props: { widget: Widget }) {
    const { settings, updateSettings } = useWidgetSettings<GameWidgetSettings>(props.widget)
    const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false)
    const [selectedSkinId, setSelectedSkinId] = useState<string>('')

    const handleApplySkin = (skinId: string) => {
        if (!skinId || !settings?.gameConfig) {
            return
        }

        const selectedSkin = skinsDirectory.find(skin => skin.id === skinId)
        if (!selectedSkin) {
            return
        }

        const newConfig = applySkin(settings.gameConfig, selectedSkin)
        updateSettings({
            gameConfig: newConfig,
        })
        setSelectedSkinId(skinId)
        setIsPopupOpen(false)
    }

    return (
        <div className="space-y-2">
            <div className="flex items-center justify-between">
                <Label>Game Skins</Label>
                <Button
                    onClick={() => setIsPopupOpen(true)}
                    variant="secondary"
                    size="sm"
                >
                    Browse Skins
                </Button>
            </div>

            {selectedSkinId && (
                <div className="text-xs text-muted-foreground">
                    Applied: {skinsDirectory.find(s => s.id === selectedSkinId)?.name}
                </div>
            )}

            {isPopupOpen && (
                <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
                    <div className="bg-card border rounded-md shadow-lg max-w-xs w-full mx-4 max-h-[70vh] overflow-hidden">
                        <div className="flex items-center justify-between px-3 py-2 border-b">
                            <h3 className="text-sm font-medium">Choose Skin</h3>
                            <Button
                                onClick={() => setIsPopupOpen(false)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                            >
                                ×
                            </Button>
                        </div>

                        <div className="p-2 max-h-80 overflow-y-auto">
                            <div className="space-y-1">
                                {skinsDirectory.map((skin) => (
                                    <button
                                        key={skin.id}
                                        onClick={() => handleApplySkin(skin.id)}
                                        className={`w-full text-left px-2 py-1.5 rounded text-sm transition-colors ${
                                            selectedSkinId === skin.id
                                                ? 'bg-primary/10 text-primary'
                                                : 'hover:bg-accent/50'
                                        }`}
                                    >
                                        <div className="flex items-center justify-between">
                                            <span className="truncate">{skin.name}</span>
                                            {selectedSkinId === skin.id && (
                                                <span className="text-primary text-xs ml-2">✓</span>
                                            )}
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </div>

                        <div className="border-t px-2 py-2">
                            <Button
                                onClick={() => setIsPopupOpen(false)}
                                variant="ghost"
                                size="sm"
                                className="w-full text-xs"
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

function SizeControlGameWidget({ widget }: { widget: Widget }) {
    return <SizeControl widget={widget} availableSizeModes={['fixed', 'fluid']} />
}

addPropertyControls(GameWidget, {
    skins: {
        name: 'Skins',
        controls: [SkinSelectorControl],
    },
    game: {
        name: 'Game',
        controls: [GameTypeControl],
    },
    gameSpecific: {
        name: 'Game Specific',
        controls: [GameSettingsControl],
    },
    layout: {
        name: 'Layout',
        controls: [SizeControlGameWidget, MarginControl, PaddingControl],
    },
    appearance: {
        name: 'Appearance',
        controls: [BorderRadiusControl],
    },
})
